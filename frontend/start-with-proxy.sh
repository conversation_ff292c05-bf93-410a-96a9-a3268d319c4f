#!/bin/bash

# 代理环境下启动前端开发服务器的脚本
# 使用方法: ./start-with-proxy.sh

echo "🚀 启动代理环境下的前端开发服务器..."

# 加载代理环境配置
if [ -f .env.proxy ]; then
    echo "📝 加载代理环境配置..."
    export $(cat .env.proxy | grep -v '^#' | xargs)
fi

# 设置额外的环境变量
export VITE_HMR_CLIENT_PORT=24678
export VITE_HMR_OVERLAY=false

echo "🔧 HMR 配置:"
echo "  - HMR Host: ${VITE_HMR_HOST:-127.0.0.1}"
echo "  - HMR Port: ${VITE_HMR_PORT:-24678}"
echo "  - HMR Protocol: ${VITE_HMR_PROTOCOL:-ws}"
echo "  - API Base URL: ${VITE_API_BASE_URL:-http://127.0.0.1:8000}"

echo "🌐 请确保在 Clash Verge 中添加以下规则绕过本地开发服务器:"
echo "  - DOMAIN-SUFFIX,localhost,DIRECT"
echo "  - DOMAIN-SUFFIX,127.0.0.1,DIRECT"
echo "  - IP-CIDR,*********/8,DIRECT"
echo "  - DST-PORT,5173,DIRECT"
echo "  - DST-PORT,24678,DIRECT"

echo ""
echo "🎯 启动开发服务器..."

# 启动 Vite 开发服务器
npm run dev -- --host 0.0.0.0 --port 5173
