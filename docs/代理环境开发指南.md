# 代理环境下前端开发指南

## 问题描述

当使用 Clash Verge 等代理工具时，前端开发服务器会出现以下问题：
- 浏览器控制台显示：`[vite] server connection lost. polling for restart...`
- 页面不断刷新
- 热重载功能失效

## 问题原因

1. **HMR WebSocket 连接被代理拦截**：Vite 的热重载依赖 WebSocket 连接，代理可能拦截这些连接
2. **本地地址被代理**：代理规则可能将 localhost/127.0.0.1 的请求也代理了
3. **端口冲突**：HMR 端口可能与代理配置冲突

## 解决方案

### 方案1：配置 Clash Verge 绕过规则（推荐）

1. **打开 Clash Verge 配置文件**
   - 点击 "配置" -> "打开配置文件夹"
   - 编辑当前使用的配置文件（通常是 .yaml 文件）

2. **添加绕过规则**
   在 `rules:` 部分的**最前面**添加：
   ```yaml
   rules:
     # 本地开发服务器绕过代理
     - DOMAIN-SUFFIX,localhost,DIRECT
     - DOMAIN-SUFFIX,127.0.0.1,DIRECT
     - IP-CIDR,*********/8,DIRECT
     - IP-CIDR,***********/16,DIRECT
     
     # 开发服务器端口
     - DST-PORT,5173,DIRECT
     - DST-PORT,24678,DIRECT
     - DST-PORT,8000,DIRECT
     
     # 其他原有规则...
   ```

3. **重启 Clash Verge** 使配置生效

### 方案2：使用系统代理绕过列表

1. **在 Clash Verge 中设置**
   - 打开 "设置" -> "系统代理"
   - 在 "绕过域名/IP" 中添加：
     ```
     localhost
     127.0.0.1
     192.168.*
     10.*
     ```

### 方案3：使用专门的开发配置

1. **使用代理环境启动脚本**
   ```bash
   cd frontend
   npm run dev:proxy
   ```

2. **或者手动设置环境变量**
   ```bash
   cd frontend
   export VITE_HMR_HOST=127.0.0.1
   export VITE_HMR_PORT=24678
   npm run dev
   ```

### 方案4：临时关闭代理

开发时临时关闭 Clash Verge 的系统代理：
- 点击 Clash Verge 托盘图标
- 关闭 "系统代理"
- 开发完成后重新开启

## Docker 环境特殊配置

如果使用 Docker 开发环境，需要确保：

1. **HMR 端口映射正确**
   ```yaml
   ports:
     - "5173:5173"
     - "24678:24678"  # HMR 端口
   ```

2. **环境变量配置**
   ```yaml
   environment:
     VITE_HMR_HOST: "0.0.0.0"
     VITE_HMR_PORT: "24678"
     VITE_USE_POLLING: "true"
   ```

## 验证解决方案

1. **检查 HMR 连接**
   - 打开浏览器开发者工具
   - 查看 Network 标签页
   - 应该能看到成功的 WebSocket 连接到 `ws://localhost:24678`

2. **测试热重载**
   - 修改前端代码
   - 保存文件
   - 页面应该自动更新，不会刷新

3. **检查控制台**
   - 不应该再看到 "server connection lost" 错误
   - 应该看到 "[vite] connected" 消息

## 常见问题

### Q: 配置后仍然有问题怎么办？
A: 
1. 确保规则添加在配置文件的最前面
2. 重启 Clash Verge
3. 清除浏览器缓存
4. 重启开发服务器

### Q: Docker 环境下仍然连接失败？
A: 
1. 检查端口映射是否正确
2. 确保防火墙没有阻止端口
3. 尝试使用 `127.0.0.1` 而不是 `localhost`

### Q: 其他代理工具怎么配置？
A: 
- **V2rayN**: 在设置中添加绕过域名
- **Shadowsocks**: 配置 PAC 文件绕过本地地址
- **Surge**: 添加 DOMAIN-SUFFIX 规则

## 最佳实践

1. **开发时使用专门的代理配置**，包含本地开发绕过规则
2. **团队共享代理配置模板**，确保所有开发者使用相同配置
3. **定期更新绕过规则**，包含新的开发端口
4. **使用环境变量**控制不同环境下的 HMR 配置
