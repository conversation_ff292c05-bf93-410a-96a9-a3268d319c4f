# Clash Verge 规则配置示例
# 用于解决代理环境下前端开发服务器连接问题

# 在 Clash Verge 的配置文件中添加以下规则到 rules 部分的最前面：

rules:
  # 本地开发服务器绕过代理
  - DOMAIN-SUFFIX,localhost,DIRECT
  - DOMAIN-SUFFIX,127.0.0.1,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  
  # 前端开发服务器端口
  - DST-PORT,5173,DIRECT
  - DST-PORT,24678,DIRECT
  - DST-PORT,3000,DIRECT
  - DST-PORT,8080,DIRECT
  
  # 后端开发服务器端口
  - DST-PORT,8000,DIRECT
  - DST-PORT,8001,DIRECT
  
  # 数据库端口
  - DST-PORT,5432,DIRECT
  - DST-PORT,5433,DIRECT
  - DST-PORT,3306,DIRECT
  - DST-PORT,27017,DIRECT
  
  # WebSocket 连接
  - PROCESS-NAME,node,DIRECT
  - PROCESS-NAME,npm,DIRECT
  - PROCESS-NAME,yarn,DIRECT
  - PROCESS-NAME,pnpm,DIRECT
  
  # 其他规则...
  # (这里放置你原有的代理规则)

# 配置说明：
# 1. DIRECT 表示直连，不走代理
# 2. 将这些规则放在配置文件的最前面，确保优先匹配
# 3. 重启 Clash Verge 使配置生效

# 如果使用 Clash Verge Rev 版本，还可以在 "设置" -> "系统代理" 中
# 添加以下域名到绕过列表：
# localhost
# 127.0.0.1
# 192.168.*
# 10.*
